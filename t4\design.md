# FastGPT智能风电工程报价参考系统设计方案

## 一、项目目标

构建一个基于FastGPT的智能体，能够自动整合《陆上风电场工程概算定额（去水印）》《陆上风电场工程设计概算编制规定及费用标准（去水印）》和《2023年第三批设备材料参考价格最新版5.11》三类数据，实现风电工程项目的智能报价参考依据检索与自动生成。

---

## 二、知识库构建

### 1. 文档预处理与结构化
- **PDF去水印**：确保文档内容清晰可提取。
- **表格文件（Excel）**：直接作为结构化数据导入。
- **文档内容结构化**：
  - 对于PDF文档，建议用OCR/文本提取工具将内容转为可检索文本。
  - 对于费用标准、定额方法等，建议按章节、条款、表格等粒度切分，便于后续检索和引用。

### 2. 知识库导入
- 利用FastGPT支持的多文档上传功能，将上述文档（PDF、Excel）上传至知识库。
- 文档解析节点会自动将文档内容转为可检索的文本片段，并按文件名、内容分隔。
- 结构化数据（如Excel材料价格）建议以"材料名称-规格-价格-单位-来源"字段为主，便于后续检索和引用。

---

## 三、工作流设计

### 1. 工作流核心节点

1. **用户输入需求**
   - 例如："请为某地陆上风电场项目编制一份智能报价参考，包括人工、材料、设备等主要费用。"
2. **文档解析节点**
   - 自动解析上传的PDF、Excel等文档，提取定额、费用标准、材料价格等信息。
3. **智能检索节点**
   - 基于用户需求，自动检索相关的定额条款、费用标准、材料价格等内容。
   - 支持多轮追问和上下文引用。
4. **自动整合与计算节点**
   - 结合定额计算方法、费用标准和材料价格，自动生成报价参考。
   - 可通过代码节点实现自动化计算（如Python代码节点，读取Excel并计算总价）。
5. **结果生成与输出节点**
   - 以结构化表格或文本形式输出智能报价参考依据，并附引用来源。

### 2. 工作流示意

```
用户输入需求
   ↓
文档解析节点（解析三类文档）
   ↓
智能检索节点（定额、标准、价格）
   ↓
自动整合与计算节点（代码/LLM推理）
   ↓
结果输出节点（表格/文本/引用）
```

---

## 四、智能体实现建议

### 1. 知识库问答能力
- FastGPT支持基于知识库的问答，能自动引用文档内容。
- 可通过"文档解析节点"+"AI对话节点"实现"引用型问答"，即每次回答都能附带出处。

### 2. 自动化计算能力
- 利用FastGPT的"代码执行节点"，可自动读取Excel材料价格，结合定额和费用标准进行自动报价计算。
- 代码节点可用Python实现，示例伪代码如下：

```python
import pandas as pd
# 读取材料价格表
df = pd.read_excel('2023年第三批设备材料参考价格最新版5.11.xlsx')
# 假设用户输入了材料清单
materials = ['风机', '塔筒', '电缆']
result = df[df['材料名称'].isin(materials)]
total_cost = result['单价'].sum()
print(f"主要材料总价：{total_cost}元")
```

- 结合定额和人工费标准，可进一步自动化生成完整报价。

### 3. 多轮对话与追问
- FastGPT支持多轮对话，用户可进一步追问"人工费怎么算？"、"某材料价格有无最新标准？"等，系统可自动检索并引用知识库内容。

---

## 五、落地实施关键步骤

1. **文档整理与去水印**：确保所有文档内容可被提取和检索。
2. **知识库搭建**：将三类文档上传至FastGPT知识库，完成文档解析。
3. **工作流搭建**：
   - 配置文档解析节点，确保所有文档内容可被AI引用。
   - 配置AI对话节点，实现基于知识库的问答。
   - 配置代码节点，实现自动化材料价格、人工费等计算。
4. **测试与优化**：通过实际项目需求测试工作流，优化检索与计算逻辑。
5. **上线与应用**：将智能体部署上线，供项目团队或客户使用。

---

## 六、专业建议与扩展

- **数据更新**：材料价格、人工费等建议定期更新，保持知识库时效性。
- **自定义模板**：可根据企业实际需求定制报价模板，支持导出Word/Excel等格式。
- **权限与安全**：敏感数据建议设置访问权限，保障数据安全。
- **多模型支持**：可结合GPT、Claude等多模型，提升问答准确性和多样性。

---

## 七、示例用户对话

**用户**：请为某地陆上风电场项目编制一份智能报价参考，包括人工、材料、设备等主要费用。

**智能体**：
1. 主要材料价格（引用Excel最新数据）
2. 人工费标准（引用《费用标准》）
3. 定额计算方法（引用《概算定额》）
4. 计算结果及明细表
5. 所有数据均附带出处，便于溯源 

---

## 八、多轮对话实现方案

### 1. 原理说明
- **上下文追踪**：FastGPT自动记录每一轮对话内容，历史对话会作为上下文传递给大模型，帮助理解当前问题的语境。
- **知识库引用**：多轮对话中，FastGPT可引用本轮及历史轮次上传或解析的文档内容，实现"记忆力"。
- **问题优化**：内置"问题优化"模块，自动将追问补全为完整问题，提升检索准确性。

### 2. 配置方法
- 在FastGPT"应用"中选择"知识库+对话引导"模板，系统自动开启多轮对话能力。
- 工作流中"文档解析节点"会自动解析本轮和历史轮次上传的文档。
- "AI对话节点"支持引用历史对话和知识库检索结果，自动拼接上下文。
- FastGPT会在每轮检索前自动进行"指代消解"和"问题扩展"，确保追问能检索到正确内容。

### 3. 实用技巧
- **引用历史文档**：用户首轮上传文档，后续轮次无需重复上传，系统自动引用。
- **多轮追问**：用户可连续追问细节，系统自动补全问题并检索。
- **输出引用**：每轮回答可自动附带引用来源，便于溯源和核查。

### 4. 示例对话

用户：请为某地陆上风电场项目编制一份智能报价参考。
AI：请问您需要包含哪些费用？（如人工、材料、设备等）
用户：人工和主要材料。
AI：人工费标准如下（引用费用标准文档），主要材料价格如下（引用Excel最新数据）。
用户：人工费怎么算？
AI：人工费的计算方法如下（引用定额文档相关条款）……

### 5. 进阶建议
- 可在"AI对话节点"自定义提示词，强化模型对历史对话的引用和理解。
- 可调整"历史对话轮数"参数，决定每次传递给模型的历史对话长度。

### 如何设计AI主动提问的提示词

为让AI在对话中主动追问用户需求，可以在系统提示词或AI对话节点中加入如下引导：

**系统提示词/Prompt示例：**

你是风电工程智能报价助手。你的任务是根据用户输入的需求，自动生成详细的报价参考。如果用户的需求描述不完整，特别是未明确说明需要包含哪些费用时，请主动追问，例如：
"请问您需要包含哪些费用？（如人工、材料、设备等）"
你可以根据实际情况，灵活追问其他关键信息（如项目规模、地点、特殊要求等），以便生成更准确的报价。

**进阶追问模板：**
- "请问您需要包含哪些费用？（如人工、材料、设备等）"
- "请问项目的规模和装机容量是多少？"
- "是否有特殊的材料或设备要求？"
- "请问项目地点在哪里？（不同地区费用标准可能不同）"

通过上述提示词设计，AI能在信息不全时主动追问，提升对话智能性和用户体验。 