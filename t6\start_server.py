#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的HTTP服务器启动脚本
"""
import http.server
import socketserver
import webbrowser
import os
import time
from threading import Timer

def open_browser():
    """延迟打开浏览器"""
    webbrowser.open('http://localhost:8000')

def start_server():
    """启动HTTP服务器"""
    PORT = 8000
    
    # 确保在正确的目录中
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    class MyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
        def end_headers(self):
            # 设置CORS头部以允许跨域请求
            self.send_header('Access-Control-Allow-Origin', '*')
            self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
            self.send_header('Access-Control-Allow-Headers', 'Content-Type')
            super().end_headers()
        
        def log_message(self, format, *args):
            # 自定义日志格式
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")
    
    with socketserver.TCPServer(("", PORT), MyHTTPRequestHandler) as httpd:
        print("=" * 60)
        print("🚀 充电桩运维监控数字大屏服务器启动成功!")
        print("=" * 60)
        print(f"📱 服务器地址: http://localhost:{PORT}")
        print(f"📁 服务目录: {os.getcwd()}")
        print("=" * 60)
        print("⚡ 功能特性:")
        print("  • 实时日志分析和可视化")
        print("  • 设备故障统计和排行")
        print("  • 多维度图表展示")
        print("  • 动态日志滚动显示")
        print("  • 响应式设计支持")
        print("=" * 60)
        print("🎯 使用说明:")
        print("  • 浏览器将自动打开数字大屏")
        print("  • 按空格键暂停/继续日志滚动")
        print("  • 点击设备排行榜查看详情")
        print("  • 按Ctrl+C停止服务器")
        print("=" * 60)
        
        # 延迟2秒后打开浏览器
        Timer(2.0, open_browser).start()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n" + "=" * 60)
            print("🛑 服务器已停止")
            print("感谢使用充电桩运维监控数字大屏!")
            print("=" * 60)

if __name__ == "__main__":
    start_server() 