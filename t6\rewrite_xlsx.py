import sys
import os
import openpyxl


def rewrite_xlsx(input_path):
    # 加载工作簿和第一个sheet
    wb = openpyxl.load_workbook(input_path)
    ws = wb.active

    # 获取表头
    headers = [cell.value for cell in ws[1]]

    # 遍历每一行（从第二行开始）
    for row in ws.iter_rows(min_row=2, max_row=ws.max_row):
        for idx, cell in enumerate(row):
            field = headers[idx] if idx < len(headers) else f"字段{idx+1}"
            original = cell.value if cell.value is not None else ""
            cell.value = f"{field}：{original}"

    # 生成输出文件名
    base, ext = os.path.splitext(input_path)
    output_path = f"{base}_rewrite{ext}"
    wb.save(output_path)
    print(f"已保存改写后的文件：{output_path}")


def main():
    if len(sys.argv) != 2:
        print("用法: python rewrite_xlsx.py <xlsx文件路径>")
        return
    input_path = sys.argv[1]
    if not os.path.isfile(input_path):
        print(f"文件不存在: {input_path}")
        return
    rewrite_xlsx(input_path)


if __name__ == "__main__":
    main()
