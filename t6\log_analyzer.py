#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
充电桩日志分析工具
"""
import re
import json
import os
from datetime import datetime
from collections import defaultdict, Counter
import csv

class LogAnalyzer:
    def __init__(self, log_file):
        self.log_file = log_file
        self.data = {
            'error_stats': defaultdict(int),
            'warn_stats': defaultdict(int),
            'device_faults': defaultdict(list),
            'hourly_stats': defaultdict(lambda: {'error': 0, 'warn': 0}),
            'fault_types': defaultdict(int),
            'error_types': defaultdict(int),
            'device_list': set(),
            'total_logs': 0,
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
    def analyze_log(self):
        """分析日志文件"""
        print("开始分析日志文件...")
        
        # 定义正则表达式模式
        log_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}) (ERROR|WARN|INFO|DEBUG)\s+(.+)'
        device_fault_pattern = r'设备(\d+)端口(\d+)[,，]\s*故障\s*(.+)'
        billing_error_pattern = r'设备(\d+)\s+场地\s+(\d+)\s+未设置\s+(.+)\s+计费模板'
        device_not_exist_pattern = r'设备(\d+)端口(\d+)不存在'
        service_error_pattern = r'(发送微信消息失败|Execution of Rabbit message listener failed|InternalServerError)'
        
        try:
            with open(self.log_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if not line:
                        continue
                        
                    self.data['total_logs'] += 1
                    
                    # 解析基本日志信息
                    match = re.match(log_pattern, line)
                    if not match:
                        continue
                        
                    timestamp, level, message = match.groups()
                    hour = timestamp.split()[1].split(':')[0]
                    
                    # 统计每小时的错误和警告数量
                    self.data['hourly_stats'][hour][level.lower()] += 1
                    
                    if level == 'ERROR':
                        self._analyze_error(message)
                    elif level == 'WARN':
                        self._analyze_warning(message)
                        
                    # 分析设备故障
                    fault_match = re.search(device_fault_pattern, message)
                    if fault_match:
                        device_id, port, fault_type = fault_match.groups()
                        self.data['device_list'].add(device_id)
                        self.data['device_faults'][device_id].append({
                            'port': port,
                            'fault': fault_type.strip(),
                            'time': timestamp
                        })
                        self.data['fault_types'][fault_type.strip()] += 1
                        
                    # 分析计费模板错误
                    billing_match = re.search(billing_error_pattern, message)
                    if billing_match:
                        device_id, venue_id, template_type = billing_match.groups()
                        self.data['device_list'].add(device_id)
                        self.data['error_types']['计费模板未设置'] += 1
                        
                    # 分析端口不存在错误
                    not_exist_match = re.search(device_not_exist_pattern, message)
                    if not_exist_match:
                        device_id, port = not_exist_match.groups()
                        self.data['device_list'].add(device_id)
                        self.data['error_types']['设备端口不存在'] += 1
                        
                    # 分析服务错误
                    service_match = re.search(service_error_pattern, message)
                    if service_match:
                        error_type = service_match.group(1)
                        if '微信' in error_type:
                            self.data['error_types']['微信服务错误'] += 1
                        elif 'Rabbit' in error_type:
                            self.data['error_types']['RabbitMQ错误'] += 1
                        elif 'InternalServerError' in error_type:
                            self.data['error_types']['内部服务错误'] += 1
                            
        except Exception as e:
            print(f"分析日志时出错: {e}")
            
        # 转换set为list以便JSON序列化
        self.data['device_list'] = list(self.data['device_list'])
        
        print(f"日志分析完成! 总共处理了 {self.data['total_logs']} 条日志")
        
    def _analyze_error(self, message):
        """分析ERROR级别日志"""
        self.data['error_stats']['total'] += 1
        
        if '计费模板' in message:
            self.data['error_stats']['billing_template'] += 1
        elif '不存在' in message:
            self.data['error_stats']['device_not_exist'] += 1
        elif 'channel error' in message:
            self.data['error_stats']['channel_error'] += 1
        else:
            self.data['error_stats']['other'] += 1
            
    def _analyze_warning(self, message):
        """分析WARN级别日志"""
        self.data['warn_stats']['total'] += 1
        
        if '故障' in message:
            self.data['warn_stats']['device_fault'] += 1
        elif '微信消息失败' in message:
            self.data['warn_stats']['wechat_fail'] += 1
        elif 'Rabbit message listener failed' in message:
            self.data['warn_stats']['rabbitmq_fail'] += 1
        else:
            self.data['warn_stats']['other'] += 1
            
    def save_analysis_results(self):
        """保存分析结果到JSON文件"""
        output_file = 'log_analysis_results.json'
        
        # 转换defaultdict为普通dict以便JSON序列化
        json_data = {
            'error_stats': dict(self.data['error_stats']),
            'warn_stats': dict(self.data['warn_stats']),
            'device_faults': dict(self.data['device_faults']),
            'hourly_stats': dict(self.data['hourly_stats']),
            'fault_types': dict(self.data['fault_types']),
            'error_types': dict(self.data['error_types']),
            'device_list': self.data['device_list'],
            'total_logs': self.data['total_logs'],
            'analysis_time': self.data['analysis_time'],
            'summary': {
                'total_devices': len(self.data['device_list']),
                'total_faults': sum(self.data['fault_types'].values()),
                'total_errors': self.data['error_stats']['total'],
                'total_warnings': self.data['warn_stats']['total']
            }
        }
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
            
        print(f"分析结果已保存到: {output_file}")
        return output_file
        
    def generate_csv_report(self):
        """生成CSV格式的设备故障报告"""
        csv_file = 'device_fault_report.csv'
        
        with open(csv_file, 'w', newline='', encoding='utf-8-sig') as f:
            writer = csv.writer(f)
            writer.writerow(['设备ID', '端口', '故障类型', '发生时间'])
            
            for device_id, faults in self.data['device_faults'].items():
                for fault in faults:
                    writer.writerow([device_id, fault['port'], fault['fault'], fault['time']])
                    
        print(f"设备故障报告已保存到: {csv_file}")
        return csv_file

def main():
    log_file = 'charge-server_20250522121252.log'
    
    if not os.path.exists(log_file):
        print(f"日志文件 {log_file} 不存在!")
        return
        
    analyzer = LogAnalyzer(log_file)
    analyzer.analyze_log()
    
    # 保存分析结果
    json_file = analyzer.save_analysis_results()
    csv_file = analyzer.generate_csv_report()
    
    print("\n=== 分析摘要 ===")
    print(f"总日志条数: {analyzer.data['total_logs']}")
    print(f"涉及设备数: {len(analyzer.data['device_list'])}")
    print(f"总错误数: {analyzer.data['error_stats']['total']}")
    print(f"总警告数: {analyzer.data['warn_stats']['total']}")
    print(f"设备故障数: {sum(analyzer.data['fault_types'].values())}")
    
    print("\n=== 主要故障类型 ===")
    for fault_type, count in sorted(analyzer.data['fault_types'].items(), key=lambda x: x[1], reverse=True)[:5]:
        print(f"{fault_type}: {count}次")
        
    return json_file, csv_file

if __name__ == '__main__':
    main() 