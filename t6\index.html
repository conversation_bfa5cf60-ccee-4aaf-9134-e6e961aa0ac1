<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>充电桩运维监控数字大屏</title>
    <link rel="stylesheet" href="style.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
</head>
<body>
    <div class="dashboard">
        <!-- 头部标题 -->
        <header class="dashboard-header">
            <h1>充电桩运维监控数字大屏</h1>
            <div class="datetime">
                <span id="current-time"></span>
                <span>数据更新时间: <span id="update-time"></span></span>
            </div>
        </header>

        <!-- 主要统计数据卡片 -->
        <section class="stats-grid">
            <div class="stat-card total-logs">
                <div class="stat-icon">📊</div>
                <div class="stat-content">
                    <h3>总日志条数</h3>
                    <div class="stat-number" id="total-logs">0</div>
                    <div class="stat-label">条</div>
                </div>
            </div>
            
            <div class="stat-card total-devices">
                <div class="stat-icon">🔌</div>
                <div class="stat-content">
                    <h3>设备总数</h3>
                    <div class="stat-number" id="total-devices">0</div>
                    <div class="stat-label">台</div>
                </div>
            </div>
            
            <div class="stat-card total-errors">
                <div class="stat-icon">❌</div>
                <div class="stat-content">
                    <h3>错误总数</h3>
                    <div class="stat-number" id="total-errors">0</div>
                    <div class="stat-label">个</div>
                </div>
            </div>
            
            <div class="stat-card total-warnings">
                <div class="stat-icon">⚠️</div>
                <div class="stat-content">
                    <h3>警告总数</h3>
                    <div class="stat-number" id="total-warnings">0</div>
                    <div class="stat-label">个</div>
                </div>
            </div>
            
            <div class="stat-card total-faults">
                <div class="stat-icon">🔧</div>
                <div class="stat-content">
                    <h3>设备故障</h3>
                    <div class="stat-number" id="total-faults">0</div>
                    <div class="stat-label">次</div>
                </div>
            </div>
            
            <div class="stat-card fault-rate">
                <div class="stat-icon">📈</div>
                <div class="stat-content">
                    <h3>故障率</h3>
                    <div class="stat-number" id="fault-rate">0%</div>
                    <div class="stat-label">百分比</div>
                </div>
            </div>
        </section>

        <!-- 图表区域 -->
        <section class="charts-grid">
            <!-- 故障类型分布饼图 -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3>故障类型分布</h3>
                </div>
                <div id="fault-types-chart" class="chart"></div>
            </div>

            <!-- 时间趋势图 -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3>24小时错误/警告趋势</h3>
                </div>
                <div id="hourly-trend-chart" class="chart"></div>
            </div>

            <!-- 设备状态分布 -->
            <div class="chart-container">
                <div class="chart-header">
                    <h3>错误类型分布</h3>
                </div>
                <div id="error-types-chart" class="chart"></div>
            </div>

            <!-- 故障设备排行榜 -->
            <div class="chart-container ranking-container">
                <div class="chart-header">
                    <h3>故障频发设备排行榜 TOP10</h3>
                </div>
                <div id="device-ranking" class="ranking-list"></div>
            </div>
        </section>

        <!-- 实时日志滚动区域 -->
        <section class="logs-section">
            <div class="logs-header">
                <h3>🔴 实时故障日志</h3>
                <button id="pause-scroll" class="pause-btn">暂停滚动</button>
            </div>
            <div id="logs-container" class="logs-container">
                <div id="logs-content" class="logs-content"></div>
            </div>
        </section>
    </div>

    <!-- 模态窗口 - 设备详情 -->
    <div id="device-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modal-device-title">设备详情</h3>
                <span class="close-btn" id="close-modal">&times;</span>
            </div>
            <div id="modal-device-content" class="modal-body">
                <!-- 设备详细信息将在这里显示 -->
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html> 