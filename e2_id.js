/**
 * 身份证有效性判断
 * @param idCard 身份证号码
 * @returns 是否有效
 */
function isValidIDCard(idCard) {
    // 基本格式校验
    if (!/^\d{17}[\dXx]$/.test(idCard)) {
        return false;
    }
    // 提取省份代码
    var provinceCode = idCard.substring(0, 2);
    var provinceCodes = [
        "11", "12", "13", "14", "15", "21", "22", "23",
        "31", "32", "33", "34", "35", "36", "37", "41",
        "42", "43", "44", "45", "46", "50", "51", "52",
        "53", "54", "61", "62", "63", "64", "65", "71",
        "81", "82"
    ];
    if (!provinceCodes.includes(provinceCode)) {
        return false;
    }
    // 出生日期校验
    var year = parseInt(idCard.substring(6, 10));
    var month = parseInt(idCard.substring(10, 12));
    var day = parseInt(idCard.substring(12, 14));
    if (month < 1 || month > 12) {
        return false;
    }
    var maxDays = new Date(year, month, 0).getDate();
    if (day < 1 || day > maxDays) {
        return false;
    }
    // 校验码校验
    if (idCard.length === 18) {
        var factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
        var checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
        var sum = 0;
        for (var i = 0; i < 17; i++) {
            sum += parseInt(idCard.charAt(i)) * factors[i];
        }
        var checkCode = checkCodes[sum % 11];
        return checkCode === idCard.charAt(17).toUpperCase();
    }
    return true;
}
// 使用示例
// 从命令行读取输入
var readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
});
readline.question('请输入身份证号: ', function (idCard) {
    if (isValidIDCard(idCard)) {
        console.log("".concat(idCard, " \u662F\u6709\u6548\u7684\u8EAB\u4EFD\u8BC1\u53F7"));
    }
    else {
        console.log("".concat(idCard, " \u662F\u65E0\u6548\u7684\u8EAB\u4EFD\u8BC1\u53F7"));
    }
    readline.close();
});
// 110101199003074258输出：true 有效身份证
// 12345678901234567X输出: false 无效身份证
