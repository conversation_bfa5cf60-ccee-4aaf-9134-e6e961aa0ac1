# 电力设备故障智能诊断助手（基于 FastGPT）设计文档

## 一、需求挖掘

### 1. 业务目标
- **目标**：构建一个能够自动识别、分类和诊断电力设备故障类型的智能助手，辅助运维人员快速定位问题并给出解决建议。
- **主要用户**：电力设备运维工程师、技术支持人员、调度中心等。

### 2. 数据与知识库
- **数据类型**：
  - 故障代码及对应说明（如 Word、Excel、PDF 文档等）
  - 故障现象描述、处理流程、常见问答
  - 设备手册、操作规程等
- **数据来源**：本地文档、历史故障库、外部API（如设备监控系统）
- **数据更新**：需支持定期更新和手动补充

### 3. 工作流流程
- 用户输入故障现象/代码/描述
- 智能体自动识别问题类型（如：高压开关故障、变压器故障、通信故障等）
- 检索知识库，返回最相关的故障原因、处理建议
- 如有需要，支持多轮追问（如补充设备型号、运行环境等信息）
- 可选：调用外部API获取实时设备状态或历史数据

### 4. 智能体能力
- 支持多种问法（自然语言、代码、现象描述等）
- 故障类型自动分类（可用 FastGPT 的"问题分类"模块）
- 支持多轮对话和上下文理解
- 可扩展：后续可集成图片识别、语音输入等

### 5. 集成与输出
- Web 端、移动端、企业微信等平台集成
- 输出内容包括：文本、表格、图片（如流程图）、文档下载等

### 6. 权限与管理
- 支持多用户、权限分级（如普通用户/专家/管理员）
- 日志记录、问题统计、知识库维护

### 7. 其他需求
- 响应速度快，支持高并发
- 支持后续扩展（如接入更多设备类型、外部系统）

---

## 二、概要设计

### 1. 系统架构（文字描述）
- **前端**：用户通过 Web/移动端界面输入故障信息，查看诊断结果
- **FastGPT 智能体**：负责问题分类、知识库检索、对话管理
- **知识库**：存储故障代码、处理流程、常见问答等
- **外部接口**（可选）：对接设备监控系统、历史数据API
- **后台管理**：知识库维护、用户权限管理、日志统计

### 2. 主要模块划分
- **数据接入与预处理**：支持 Word、Excel、PDF、CSV 等多种格式导入，自动分段、QA 拆分
- **知识库管理**：文档上传、问答对维护、数据更新
- **工作流编排（Flow）**：
  - 问题分类（如"高压开关故障/变压器故障/通信故障/其他"）
  - 知识库检索
  - 多轮追问（如需补充信息）
  - 指定回复/外部API调用
- **智能体核心**：模型调用、上下文管理、插件扩展
- **对外接口与集成**：API、Web、企业微信等
- **权限与管理**：用户、角色、日志、统计

### 3. 关键流程说明
- **用户交互流程**：用户输入 → 问题分类 → 检索知识库 → 返回诊断建议 → 多轮追问（如有）
- **数据流转流程**：文档上传 → 自动分段/QA → 向量化入库 → 检索
- **工作流执行流程**：Flow 可视化编排，支持条件分支、插件调用

### 4. 技术选型建议
- **模型**：优先用 GPT-4/Claude/文心一言等主流 LLM
- **知识库**：FastGPT 内置向量数据库，支持本地和云端
- **部署**：Docker Compose/本地服务器/云主机
- **集成**：企业微信、Web API

### 5. 后续迭代建议
- 增加图片/语音输入
- 故障预测与预警
- 智能推荐备件、工单自动生成
- 多语言支持

---

## 三、分类优先 vs 检索优先的流程选择与建议

### 1. 两种流程对比

#### A. 先分类后检索（分类→检索）
- **流程**：用户输入问题 → 问题分类模块（如"高压开关/变压器/通信/其他"）→ 针对分类结果在对应子知识库或用特定检索策略进行检索 → 返回答案
- **优点**：
  - 针对不同类型问题，检索更精准，减少无关内容干扰。
  - 便于后续扩展（如某类问题接入外部API、专家人工审核等）。
  - 适合结构化、类型明确的场景。
- **缺点**：
  - 分类模型不准会影响检索方向。
  - 分类粒度和类别设计需合理，否则会出现"其他"类过大、细分不均等问题。

#### B. 先检索后分类（检索→分类）
- **流程**：用户输入问题 → 全局知识库检索（语义/全文/混合）→ 检索结果再做分类或过滤 → 返回答案
- **优点**：
  - 对于用户输入模糊、跨类别的问题，检索更灵活。
  - 适合知识库内容不够结构化、分类边界模糊的场景。
  - 实现简单，易于快速上线。
- **缺点**：
  - 检索范围大，可能返回无关内容，准确率受限于知识库质量和检索算法。
  - 后续如需针对不同类型问题做差异化处理，不如先分类灵活。

### 2. 电力设备故障诊断场景的建议
- 若知识库结构化良好（如不同设备/故障类型有独立文档、问答、处理流程），**建议采用"先分类后检索"**，提升检索精准度。
- 若知识库内容混杂、用户问题类型跨度大、分类难以准确覆盖所有场景，可先采用"先检索后分类"，后续再根据实际效果优化分类模块。

### 3. 推荐实践流程
1. **初期建议**：
   - 先用"先检索后分类"，快速上线，收集用户真实问题和检索效果。
   - 同时准备好分类标签和样本，逐步训练和优化分类模块。
2. **成熟阶段**：
   - 逐步切换到"先分类后检索"，针对不同类型问题优化检索策略和知识库结构。
   - 对于分类不明确的问题，仍可保留"全局检索"兜底。

### 4. 典型工作流示意

```mermaid
graph TD
A[用户输入] --> B{问题分类}
B -->|高压开关| C1[高压开关知识库检索]
B -->|变压器| C2[变压器知识库检索]
B -->|通信| C3[通信知识库检索]
B -->|其他| C4[全局知识库检索]
C1 --> D[返回答案]
C2 --> D
C3 --> D
C4 --> D
```

### 5. 总结
- 结构化场景优先"先分类后检索"，提升准确率和可扩展性。
- 内容混杂/分类难时可"先检索后分类"，快速上线，后续优化。
- FastGPT 支持灵活组合，建议实际测试两种方案，选取最优。

---

## 四、先分类后检索的 FastGPT 工作流示意

### 工作流概述

本工作流采用 FastGPT Flow 可视化编排方式，结合"问题分类"与"知识库检索"模块，实现电力设备故障智能诊断的高效流程。用户输入问题后，系统先对问题进行类型分类，再根据分类结果在对应知识库中检索答案，最终返回精准诊断建议。

### 节点设计与说明

1. **流程开始（用户输入）**
   - 入口节点，接收用户输入的故障描述、代码或现象。

2. **问题分类节点**
   - 使用 FastGPT 的"问题分类"模块。
   - 配置分类标签（如：高压开关、变压器、通信、其他），并可在系统提示词中补充每类定义。
   - 输出分类结果（如 type = breaker, transformer, communication, other）。

3. **分支判断节点**
   - 根据分类结果，分流到不同的知识库检索节点。

4. **知识库检索节点（多分支）**
   - 针对每个分类，配置独立的知识库检索节点（如高压开关知识库、变压器知识库等）。
   - 若为"其他"类，可走全局知识库检索。

5. **AI对话/答案生成节点**
   - 汇总检索结果，生成最终诊断建议。

6. **流程结束/输出节点**
   - 返回答案给用户，可支持多轮追问或补充信息。

### 工作流流程图（官方风格）

```mermaid
graph TD
A[流程开始\n用户输入] --> B[问题分类节点\n分类标签: 高压开关/变压器/通信/其他]
B -->|高压开关| C1[知识库检索\n高压开关库]
B -->|变压器| C2[知识库检索\n变压器库]
B -->|通信| C3[知识库检索\n通信库]
B -->|其他| C4[知识库检索\n全局库]
C1 --> D[AI对话/答案生成]
C2 --> D
C3 --> D
C4 --> D
D --> E[输出结果\n返回用户]
```

### 每步作用说明

- **流程开始**：收集用户输入，作为后续节点的输入变量。
- **问题分类节点**：通过 LLM 结合系统提示词，对问题进行类型归属判断。
- **分支判断**：根据分类结果，自动路由到对应的知识库检索节点。
- **知识库检索节点**：在指定知识库中进行语义/混合检索，提升相关性和准确率。
- **AI对话/答案生成**：对检索结果进行归纳、补充，生成结构化诊断建议。
- **输出节点**：将最终答案返回用户，支持继续追问或流程结束。

### FastGPT Flow 编排亮点
- 节点可视化拖拽，逻辑清晰，便于维护和扩展。
- 分类节点和检索节点可灵活复用，支持多层级分类和多知识库接入。
- 支持后续集成外部API、专家审核等扩展节点。

---

## 五、流程节点插件与可编程点分析及性能提升建议

本节结合 FastGPT Flow 工作流能力，分析"先分类后检索"流程中各节点可用的官方插件、可编程点，以及如何通过插件和代码提升系统性能与智能化水平。

### 1. 流程开始（用户输入）
- **可用插件**：系统自带"用户输入"节点。
- **可编程点**：
  - 如需支持图片、语音等多模态输入，可通过自定义前端或 API 扩展。
- **性能提升建议**：
  - 保持输入结构化，便于后续节点变量引用。

### 2. 问题分类节点
- **可用插件**：FastGPT 内置"问题分类"模块（function_call 类型）。
- **可编程点**：
  - 精细化系统提示词，补充分类定义和示例，提升分类准确率。
  - 引入历史对话（聊天记录）作为输入，增强上下文理解。
  - 如需更复杂分类，可通过 Webhook/自定义 Function 节点调用外部服务。
- **性能提升建议**：
  - 结合实际业务场景，持续优化分类标签和提示词。
  - 利用多轮上下文，提升分类鲁棒性。

### 3. 分支判断节点
- **可用插件**：Flow 条件分支（内置）。
- **可编程点**：
  - 复杂分流可用自定义代码节点实现多级/动态路由。
- **性能提升建议**：
  - 分类标签设计合理，分支逻辑清晰，减少"其他"类比例。

### 4. 知识库检索节点（多分支）
- **可用插件**：
  - FastGPT 内置"知识库检索"节点，支持语义检索、全文检索、混合检索、结果重排。
  - "问题优化"模块（指代消解、问题扩展）。
- **可编程点**：
  - 检索参数调优（相关度阈值、引用上限等）。
  - 动态选择知识库（如根据分类结果切换知识库）。
  - 检索后调用外部API，融合实时数据。
- **性能提升建议**：
  - 充分利用"问题优化"模块，提升召回率。
  - 针对不同分类配置专属知识库，提升检索精准度。
  - 检索参数根据实际数据持续调优。

### 5. AI对话/答案生成节点
- **可用插件**：FastGPT 内置"AI对话"节点。
- **可编程点**：
  - 动态生成 Prompt，结合检索结果提升答案针对性。
  - 结构化输出（如表格、流程图），可用代码节点加工。
  - 多轮追问逻辑（如置信度低时自动追问）。
- **性能提升建议**：
  - 结合检索内容动态调整 Prompt，提升专业性。
  - 输出结构化内容，便于用户理解和后续处理。

### 6. 输出节点
- **可用插件**：系统自带输出节点。
- **可编程点**：
  - Webhook/API 节点，将结果推送到外部系统（如微信、工单系统等）。
- **性能提升建议**：
  - 支持多渠道输出，提升系统集成度。

### 7. 综合建议
- **优先用内置插件**：如"问题分类""知识库检索""问题优化""AI对话"等，配置灵活，易于维护。
- **复杂场景用自定义代码节点**：如需与外部系统集成、动态知识库管理、复杂分支逻辑等，可用 Function Call、Webhook、API 节点扩展。
- **参数调优与 Prompt 工程**：持续优化检索参数、分类提示词和答案生成 Prompt，提升系统性能和用户体验。
- **流程可视化与复用**：利用 Flow 的可视化和节点复用能力，快速搭建和调整业务流程。

---

## 六、多知识库动态选择的实现方法

在电力设备故障智能诊断场景中，针对不同设备类型或问题分类，往往需要检索不同的专属知识库。FastGPT Flow 支持通过分支静态配置和变量动态引用两种方式，实现多知识库的动态选择。

### 1. 分支静态配置法（推荐起步）

- **实现思路**：
  - 问题分类节点输出 type（如 breaker/transformer/communication/other）。
  - Flow 条件分支节点，根据 type 路由到不同的"知识库检索"节点。
  - 每个"知识库检索"节点静态选择对应的知识库（如"高压开关知识库"）。
  - 检索结果合并后进入答案生成节点。
- **优点**：
  - 配置直观，全部可视化拖拽，无需代码，适合分类数量不多的场景。
- **缺点**：
  - 分类过多时，Flow 线会变多，维护成本上升。

### 2. 变量动态引用法（进阶/高级）

- **实现思路**：
  1. 分类节点输出 type。
  2. 使用自定义 Function 节点（如 HTTP/代码节点），将 type 映射为知识库ID或名称，输出为变量。
  3. "知识库检索"节点的"关联知识库"参数引用该变量，实现动态切换。
  4. 检索结果进入后续节点。
- **配置要点**：
  - Function 节点可用 HTTP 请求或代码逻辑，将 type 与知识库ID做映射。
  - 知识库检索节点支持输入参数引用前序节点输出（变量传递）。
- **优点**：
  - 支持知识库数量多、分类动态变化、自动化扩展。
- **缺点**：
  - 需一定开发能力，适合复杂业务场景。

### 3. 节点配置与流程示意

#### 分支静态配置法

```mermaid
graph TD
A[问题分类] -->|高压开关| B1[知识库检索-高压开关]
A -->|变压器| B2[知识库检索-变压器]
A -->|通信| B3[知识库检索-通信]
A -->|其他| B4[知识库检索-全局]
B1 --> C[答案生成]
B2 --> C
B3 --> C
B4 --> C
```

#### 变量动态引用法

```mermaid
graph TD
A[问题分类] --> B[Function节点: type→知识库ID]
B --> C[知识库（动态引用知识库ID）]
C --> D[答案生成]
```

- Function节点输出示例：
  ```json
  {
    "type": "breaker",
    "datasetId": "dataset_breaker_id"
  }
  ```
- 知识库检索节点"关联知识库"参数引用 `datasetId` 变量。

### 4. 实用建议
- 起步阶段建议用分支静态配置，简单直观，易于调试。
- 知识库多/需动态扩展时，用Function节点实现type到知识库ID的映射，检索节点用变量引用，支持自动化和批量管理。
- 检索结果如需合并，可用"文本加工"节点或HTTP节点处理。

---

## 七、多大模型协作的工作流设计

在复杂的电力设备故障智能诊断场景中，建议在工作流中引入至少两个大模型节点，分别承担"查询前优化"和"查询后生成"两大核心任务，实现更高的检索相关性和答案质量。

### 1. 查询前大模型（问题优化/意图识别）
- **作用**：
  - 明确用户意图、补全上下文、消除歧义、优化检索问题。
  - 典型功能包括指代消解、问题扩展、意图分类等。
- **节点类型**：AI对话节点/Function节点
- **输入**：用户原始问题、历史对话
- **输出**：优化后的检索问题
- **配置建议**：
  - 可结合"问题优化"模块或自定义Prompt，提升检索召回率和准确性。

### 2. 查询后大模型（答案生成/结构化输出）
- **作用**：
  - 对检索到的内容进行归纳、总结、结构化输出，生成最终答案。
  - 输出专业、可读性强的诊断建议，支持多模态（表格、清单等）。
- **节点类型**：AI对话节点/Function节点
- **输入**：检索结果、用户原始问题/上下文
- **输出**：最终答案
- **配置建议**：
  - 自定义Prompt，要求模型结合检索内容和用户上下文生成专业答案。
  - 可根据业务需求输出结构化内容。

### 3. 工作流结构化示意

```mermaid
graph TD
A[用户输入] --> B[查询前大模型<br>问题优化/意图识别]
B --> C[知识库检索]
C --> D[查询后大模型<br>答案生成/结构化输出]
D --> E[输出给用户]
```

### 4. 实用建议
- 两个大模型节点的Prompt要分别针对"问题优化"和"答案生成"场景精细设计。
- 可分别选用适合理解/归纳/生成的不同大模型（如GPT-4、Claude、文心一言等）。
- FastGPT Flow支持AI对话节点多次调用，灵活组合。
- 该设计为后续引入多模态理解、外部工具调用等能力打下基础。

### 查询后大模型提示词模板（专业答案生成/结构化输出）

为确保最终输出专业、准确且结构化，建议在 AI 对话节点的提示词中，结合知识库检索结果和用户原始问题，按如下模板设计：

#### 示例提示词（可直接用于 AI 对话节点）

```
你是电力设备故障智能诊断助手，具备丰富的电力设备运维知识。请根据下述背景知识和用户问题，生成专业、准确、结构化的诊断建议。

【背景知识】
"""
{{quote}}
"""

【用户问题】
{{question}}

【输出要求】
1. 首先，基于背景知识，准确回答用户问题，内容要专业、简明、可读性强。
2. 如有多条相关信息，请以结构化方式（如分点、表格、清单等）输出。
3. 若背景知识无法完全回答问题，请礼貌说明，并可给出建议或引导用户补充信息。
4. 输出内容应避免主观臆断，优先引用背景知识中的事实。
5. 如有必要，补充风险提示、注意事项或后续建议。

【输出格式示例】
- 故障类型：xxx
- 可能原因：
  1. xxx
  2. xxx
- 处理建议：
  - 步骤1：xxx
  - 步骤2：xxx
- 参考资料：{{source}}

如需表格输出，可按如下格式：
| 故障现象 | 可能原因 | 处理建议 |
| --- | --- | --- |
| xxx | xxx | xxx |

【注意】
- 仅在背景知识无法回答时，才可礼貌说明"未找到相关资料"，并建议用户补充信息或联系专家。
- 输出内容要简明、专业，便于用户直接参考和操作。
```

> 该提示词可直接配置在 FastGPT Flow 的 AI 对话节点，结合引用模板和变量，自动生成高质量、结构化的专业答案。

---
