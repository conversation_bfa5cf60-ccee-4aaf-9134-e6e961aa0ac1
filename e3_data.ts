/**
 * 计算一组数的均值和方差
 * @param numbers 数字数组
 * @returns 包含均值和方差的对象
 */

function calculateMeanAndVariance(input: string): { mean: number, variance: number } {
    // 将逗号分隔的字符串转换为数字数组
    const numbers = input.split(',').map(num => parseFloat(num.trim()));

    // 检查是否有无效数字
    if (numbers.some(isNaN)) {
      throw new Error("输入包含无效数字");
    }

    if (numbers.length === 0) {
      throw new Error("数组不能为空");
    }
  
    // 计算均值
    const sum = numbers.reduce((acc, val) => acc + val, 0);
    const mean = sum / numbers.length;
  
    // 计算方差
    const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
    const variance = squaredDiffs.reduce((acc, val) => acc + val, 0) / numbers.length;
  
    return { mean, variance };
  }
  
  