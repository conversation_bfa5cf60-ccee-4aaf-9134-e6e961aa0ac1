# 充电桩运维监控数字大屏

## 🚀 快速开始

1. 分析日志数据：`python log_analyzer.py`
2. 启动数字大屏：`python start_server.py`
3. 浏览器自动打开或访问：http://localhost:8000

## 📊 功能特性

- **实时统计**: 总日志数、设备数、错误数、警告数等关键指标
- **故障分析**: 故障类型分布饼图、时间趋势图、错误类型分析
- **设备排行**: TOP10故障频发设备排行榜
- **动态日志**: 实时滚动显示故障日志
- **交互功能**: 点击设备查看详细信息

## 📈 分析结果

- 总日志条数：60,097条
- 涉及设备数：202台
- 总错误数：1,035个
- 总警告数：11,065个
- 设备故障数：10,798次

主要故障类型：
1. 保险丝或继电器故障：6,289次
2. 继电器粘连：3,778次  
3. 485通讯错误：731次

## 🎮 操作说明

- 空格键：暂停/继续日志滚动
- Esc键：关闭弹窗
- 点击设备排行榜：查看详细信息 