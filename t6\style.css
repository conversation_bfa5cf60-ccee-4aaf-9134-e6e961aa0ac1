/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'Arial', sans-serif;
    background: linear-gradient(135deg, #0c1a3e 0%, #1a2b5e 50%, #2d4185 100%);
    color: #ffffff;
    overflow-x: hidden;
    min-height: 100vh;
}

/* 仪表盘容器 */
.dashboard {
    padding: 20px;
    min-height: 100vh;
    background: rgba(0, 0, 0, 0.1);
}

/* 头部样式 */
.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding: 20px 0;
    border-bottom: 2px solid rgba(64, 158, 255, 0.3);
}

.dashboard-header h1 {
    font-size: 2.5rem;
    font-weight: bold;
    background: linear-gradient(45deg, #409eff, #67c23a);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(64, 158, 255, 0.5);
}

.datetime {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    font-size: 1.1rem;
    color: #a0cfff;
}

.datetime span {
    margin-bottom: 5px;
}

/* 统计数据网格 */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 统计卡片 */
.stat-card {
    background: linear-gradient(135deg, rgba(64, 158, 255, 0.1), rgba(103, 194, 58, 0.1));
    border: 1px solid rgba(64, 158, 255, 0.3);
    border-radius: 15px;
    padding: 25px;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(64, 158, 255, 0.3);
    border-color: rgba(64, 158, 255, 0.6);
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #409eff, #67c23a, #e6a23c, #f56c6c);
}

.stat-icon {
    font-size: 3rem;
    margin-right: 20px;
    animation: pulse 2s infinite;
}

.stat-content h3 {
    font-size: 1.1rem;
    color: #a0cfff;
    margin-bottom: 10px;
}

.stat-number {
    font-size: 2.5rem;
    font-weight: bold;
    color: #ffffff;
    line-height: 1;
    text-shadow: 0 0 20px rgba(255, 255, 255, 0.5);
}

.stat-label {
    font-size: 0.9rem;
    color: #7ec0ee;
    margin-top: 5px;
}

/* 特定卡片颜色 */
.total-logs::before { background: linear-gradient(90deg, #409eff, #67c23a); }
.total-devices::before { background: linear-gradient(90deg, #67c23a, #e6a23c); }
.total-errors::before { background: linear-gradient(90deg, #f56c6c, #ff7875); }
.total-warnings::before { background: linear-gradient(90deg, #e6a23c, #ffd666); }
.total-faults::before { background: linear-gradient(90deg, #ff7875, #f56c6c); }
.fault-rate::before { background: linear-gradient(90deg, #9254de, #b37feb); }

/* 图表网格 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

/* 图表容器 */
.chart-container {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(64, 158, 255, 0.3);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.chart-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, #409eff, #67c23a);
}

.chart-header {
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(64, 158, 255, 0.2);
    padding-bottom: 10px;
}

.chart-header h3 {
    font-size: 1.3rem;
    color: #ffffff;
    text-align: center;
}

.chart {
    height: 300px;
    width: 100%;
}

/* 排行榜容器 */
.ranking-container .chart {
    height: auto;
}

.ranking-list {
    max-height: 300px;
    overflow-y: auto;
}

.ranking-item {
    display: flex;
    align-items: center;
    padding: 10px;
    margin-bottom: 10px;
    background: rgba(64, 158, 255, 0.1);
    border-radius: 8px;
    border-left: 3px solid;
    transition: all 0.3s ease;
    cursor: pointer;
}

.ranking-item:hover {
    background: rgba(64, 158, 255, 0.2);
    transform: translateX(5px);
}

.ranking-item:nth-child(1) { border-left-color: #ff6b6b; }
.ranking-item:nth-child(2) { border-left-color: #ffa726; }
.ranking-item:nth-child(3) { border-left-color: #ffcc02; }
.ranking-item:nth-child(n+4) { border-left-color: #409eff; }

.ranking-rank {
    font-size: 1.5rem;
    font-weight: bold;
    color: #ffffff;
    margin-right: 15px;
    min-width: 30px;
}

.ranking-device {
    flex: 1;
    font-size: 1.1rem;
    color: #a0cfff;
}

.ranking-count {
    font-size: 1.2rem;
    font-weight: bold;
    color: #f56c6c;
}

/* 日志区域 */
.logs-section {
    background: rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(64, 158, 255, 0.3);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid rgba(64, 158, 255, 0.2);
    padding-bottom: 10px;
}

.logs-header h3 {
    font-size: 1.3rem;
    color: #ffffff;
}

.pause-btn {
    background: linear-gradient(45deg, #409eff, #67c23a);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.pause-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(64, 158, 255, 0.4);
}

.logs-container {
    height: 300px;
    overflow: hidden;
    position: relative;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    border: 1px solid rgba(64, 158, 255, 0.2);
}

.logs-content {
    height: 100%;
    overflow-y: auto;
    padding: 10px;
}

.log-item {
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 5px;
    border-left: 3px solid;
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    opacity: 0;
    animation: slideIn 0.5s ease forwards;
}

.log-item.error {
    background: rgba(245, 108, 108, 0.1);
    border-left-color: #f56c6c;
    color: #ffb3b3;
}

.log-item.warn {
    background: rgba(230, 162, 60, 0.1);
    border-left-color: #e6a23c;
    color: #ffd699;
}

.log-time {
    color: #a0cfff;
    margin-right: 10px;
}

.log-device {
    color: #67c23a;
    font-weight: bold;
    margin-right: 10px;
}

.log-message {
    color: #ffffff;
}

/* 模态窗口 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.modal-content {
    background: linear-gradient(135deg, rgba(12, 26, 62, 0.95), rgba(26, 43, 94, 0.95));
    border: 1px solid rgba(64, 158, 255, 0.3);
    border-radius: 15px;
    margin: 5% auto;
    padding: 20px;
    width: 80%;
    max-width: 800px;
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid rgba(64, 158, 255, 0.3);
    padding-bottom: 10px;
}

.modal-header h3 {
    color: #ffffff;
    font-size: 1.5rem;
}

.close-btn {
    color: #a0cfff;
    font-size: 2rem;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close-btn:hover {
    color: #ffffff;
}

.modal-body {
    color: #a0cfff;
    line-height: 1.6;
}

/* 动画效果 */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(64, 158, 255, 0.3); }
    50% { box-shadow: 0 0 30px rgba(64, 158, 255, 0.6); }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #409eff, #67c23a);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #67c23a, #e6a23c);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .charts-grid {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    }
    
    .dashboard-header h1 {
        font-size: 2rem;
    }
    
    .stat-number {
        font-size: 2rem;
    }
}

@media (max-width: 768px) {
    .dashboard {
        padding: 10px;
    }
    
    .dashboard-header {
        flex-direction: column;
        text-align: center;
    }
    
    .dashboard-header h1 {
        font-size: 1.5rem;
        margin-bottom: 10px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .stat-card {
        padding: 15px;
    }
    
    .stat-number {
        font-size: 1.8rem;
    }
    
    .chart {
        height: 250px;
    }
}

/* 数据加载状态 */
.loading {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 200px;
    color: #a0cfff;
    font-size: 1.2rem;
}

.loading::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #409eff;
    border-top: 2px solid transparent;
    border-radius: 50%;
    margin-left: 10px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
} 