#!/usr/bin/env python3
"""
Solo Adventure Game - A Text-Based RPG
A complete solo coding project demonstrating:
- Object-oriented programming
- Game logic and state management
- User input handling
- Random events
"""

import random
import time
import sys

class Player:
    def __init__(self, name):
        self.name = name
        self.health = 100
        self.max_health = 100
        self.attack = 20
        self.defense = 10
        self.gold = 50
        self.level = 1
        self.experience = 0
        self.inventory = ['Health Potion']
    
    def take_damage(self, damage):
        actual_damage = max(1, damage - self.defense)
        self.health -= actual_damage
        return actual_damage
    
    def heal(self, amount):
        self.health = min(self.max_health, self.health + amount)
    
    def gain_experience(self, exp):
        self.experience += exp
        if self.experience >= self.level * 100:
            self.level_up()
    
    def level_up(self):
        self.level += 1
        self.max_health += 20
        self.health = self.max_health
        self.attack += 5
        self.defense += 3
        print(f"\n🎉 Level Up! You are now level {self.level}!")
        print(f"Health: {self.max_health}, Attack: {self.attack}, Defense: {self.defense}")
    
    def is_alive(self):
        return self.health > 0

class Enemy:
    def __init__(self, name, health, attack, defense, gold_reward, exp_reward):
        self.name = name
        self.health = health
        self.max_health = health
        self.attack = attack
        self.defense = defense
        self.gold_reward = gold_reward
        self.exp_reward = exp_reward
    
    def take_damage(self, damage):
        actual_damage = max(1, damage - self.defense)
        self.health -= actual_damage
        return actual_damage
    
    def is_alive(self):
        return self.health > 0

class Game:
    def __init__(self):
        self.player = None
        self.current_location = "village"
        self.game_running = True
        
        self.enemies = {
            "goblin": Enemy("Goblin", 30, 15, 2, 20, 25),
            "orc": Enemy("Orc", 50, 25, 5, 35, 40),
            "dragon": Enemy("Dragon", 120, 40, 10, 100, 100)
        }
        
        self.locations = {
            "village": {
                "description": "A peaceful village with a shop and inn.",
                "actions": ["shop", "inn", "forest", "cave"]
            },
            "forest": {
                "description": "A dark forest filled with mysterious creatures.",
                "actions": ["explore", "village"]
            },
            "cave": {
                "description": "A deep cave that echoes with strange sounds.",
                "actions": ["explore", "village"]
            }
        }
    
    def start_game(self):
        self.print_title()
        name = input("Enter your hero's name: ").strip()
        if not name:
            name = "Hero"
        
        self.player = Player(name)
        print(f"\nWelcome, {self.player.name}! Your adventure begins...\n")
        
        while self.game_running and self.player.is_alive():
            self.show_status()
            self.show_location()
            self.handle_action()
        
        if not self.player.is_alive():
            print("\n💀 Game Over! Your adventure ends here...")
        else:
            print("\n👋 Thanks for playing!")
    
    def print_title(self):
        title = """
    ╔══════════════════════════════════════╗
    ║          SOLO ADVENTURE GAME         ║
    ║        A Text-Based RPG Quest        ║
    ╚══════════════════════════════════════╝
        """
        print(title)
    
    def show_status(self):
        print(f"\n{'='*50}")
        print(f"🧙 {self.player.name} (Level {self.player.level})")
        print(f"❤️  Health: {self.player.health}/{self.player.max_health}")
        print(f"⚔️  Attack: {self.player.attack} | 🛡️  Defense: {self.player.defense}")
        print(f"💰 Gold: {self.player.gold} | ⭐ Experience: {self.player.experience}")
        print(f"🎒 Inventory: {', '.join(self.player.inventory)}")
        print(f"{'='*50}")
    
    def show_location(self):
        location = self.locations[self.current_location]
        print(f"\n📍 Current Location: {self.current_location.title()}")
        print(f"📝 {location['description']}")
        print(f"\n🎯 Available actions: {', '.join(location['actions'])}")
    
    def handle_action(self):
        action = input("\nWhat would you like to do? ").lower().strip()
        
        if action == "quit":
            self.game_running = False
            return
        
        location = self.locations[self.current_location]
        
        if action in location["actions"]:
            if action in ["forest", "cave", "village"]:
                self.current_location = action
                print(f"\n🚶 You travel to the {action}...")
            elif action == "explore":
                self.explore()
            elif action == "shop":
                self.shop()
            elif action == "inn":
                self.inn()
        else:
            print("\n❌ Invalid action. Try again.")
    
    def explore(self):
        print("\n🔍 You venture deeper into the unknown...")
        time.sleep(1)
        
        encounter_chance = random.random()
        
        if encounter_chance < 0.6:  # 60% chance of enemy encounter
            if self.current_location == "forest":
                enemy_type = random.choice(["goblin", "orc"])
            else:  # cave
                enemy_type = random.choice(["orc", "dragon"])
            
            enemy = Enemy(**self.enemies[enemy_type].__dict__)
            self.combat(enemy)
        
        elif encounter_chance < 0.8:  # 20% chance of treasure
            self.find_treasure()
        
        else:  # 20% chance of nothing
            print("🌫️ You find nothing of interest and return safely.")
    
    def combat(self, enemy):
        print(f"\n⚔️ A wild {enemy.name} appears!")
        print(f"💀 {enemy.name} - Health: {enemy.health}, Attack: {enemy.attack}")
        
        while enemy.is_alive() and self.player.is_alive():
            print(f"\n🎯 Combat Options: [attack] [defend] [use potion] [flee]")
            action = input("Choose your action: ").lower().strip()
            
            if action == "attack":
                damage = self.player.attack + random.randint(-5, 5)
                actual_damage = enemy.take_damage(damage)
                print(f"⚔️ You deal {actual_damage} damage to the {enemy.name}!")
                
                if enemy.is_alive():
                    enemy_damage = enemy.attack + random.randint(-3, 3)
                    player_damage = self.player.take_damage(enemy_damage)
                    print(f"💥 The {enemy.name} deals {player_damage} damage to you!")
            
            elif action == "defend":
                print("🛡️ You raise your guard!")
                enemy_damage = max(1, (enemy.attack + random.randint(-3, 3)) // 2)
                player_damage = self.player.take_damage(enemy_damage)
                print(f"💥 The {enemy.name} deals {player_damage} damage (reduced by defense)!")
            
            elif action == "use potion" and "Health Potion" in self.player.inventory:
                self.player.heal(40)
                self.player.inventory.remove("Health Potion")
                print("🧪 You drink a health potion and recover 40 health!")
                
                enemy_damage = enemy.attack + random.randint(-3, 3)
                player_damage = self.player.take_damage(enemy_damage)
                print(f"💥 The {enemy.name} deals {player_damage} damage to you!")
            
            elif action == "flee":
                if random.random() < 0.7:  # 70% chance to flee successfully
                    print("🏃 You successfully flee from combat!")
                    return
                else:
                    print("❌ You failed to flee!")
                    enemy_damage = enemy.attack + random.randint(-3, 3)
                    player_damage = self.player.take_damage(enemy_damage)
                    print(f"💥 The {enemy.name} deals {player_damage} damage to you!")
            
            else:
                print("❌ Invalid action or no potions available!")
                continue
            
            print(f"\n💖 Your Health: {self.player.health}/{self.player.max_health}")
            if enemy.is_alive():
                print(f"💀 {enemy.name} Health: {enemy.health}/{enemy.max_health}")
        
        if not enemy.is_alive():
            print(f"\n🎉 You defeated the {enemy.name}!")
            self.player.gold += enemy.gold_reward
            self.player.gain_experience(enemy.exp_reward)
            print(f"💰 You gained {enemy.gold_reward} gold and {enemy.exp_reward} experience!")
            
            # Chance to find a potion
            if random.random() < 0.3:
                self.player.inventory.append("Health Potion")
                print("🧪 You found a Health Potion!")
    
    def find_treasure(self):
        treasure_gold = random.randint(10, 50)
        self.player.gold += treasure_gold
        print(f"\n💎 You found a treasure chest with {treasure_gold} gold!")
        
        if random.random() < 0.5:
            self.player.inventory.append("Health Potion")
            print("🧪 The chest also contained a Health Potion!")
    
    def shop(self):
        print("\n🏪 Welcome to the Village Shop!")
        print("1. Health Potion - 30 gold")
        print("2. Attack Boost (permanent) - 100 gold")
        print("3. Defense Boost (permanent) - 80 gold")
        print("4. Leave shop")
        
        choice = input("What would you like to buy? (1-4): ").strip()
        
        if choice == "1" and self.player.gold >= 30:
            self.player.gold -= 30
            self.player.inventory.append("Health Potion")
            print("🧪 You bought a Health Potion!")
        elif choice == "2" and self.player.gold >= 100:
            self.player.gold -= 100
            self.player.attack += 10
            print("⚔️ Your attack increased by 10!")
        elif choice == "3" and self.player.gold >= 80:
            self.player.gold -= 80
            self.player.defense += 5
            print("🛡️ Your defense increased by 5!")
        elif choice == "4":
            print("👋 Come back anytime!")
        else:
            print("❌ Invalid choice or insufficient gold!")
    
    def inn(self):
        print("\n🏨 Welcome to the Village Inn!")
        print("Rest here to fully restore your health for 20 gold.")
        
        if input("Would you like to rest? (y/n): ").lower() == 'y':
            if self.player.gold >= 20:
                self.player.gold -= 20
                self.player.health = self.player.max_health
                print("😴 You rest peacefully and recover all your health!")
            else:
                print("❌ You don't have enough gold to rest here.")
        else:
            print("👋 Maybe next time!")

def main():
    try:
        game = Game()
        game.start_game()
    except KeyboardInterrupt:
        print("\n\n👋 Game interrupted. Thanks for playing!")
    except Exception as e:
        print(f"\n❌ An error occurred: {e}")

if __name__ == "__main__":
    main()