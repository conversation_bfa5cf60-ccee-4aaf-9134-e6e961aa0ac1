﻿{
  "nodes": [
    {
      "nodeId": "userGuide",
      "name": "common:core.module.template.system_config",
      "intro": "common:core.module.template.system_config_info",
      "avatar": "core/workflow/template/systemConfig",
      "flowNodeType": "userGuide",
      "position": {
        "x": 37.15442200052121,
        "y": -444.7446492944009
      },
      "version": "481",
      "inputs": [
        {
          "key": "welcomeText",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "string",
          "label": "core.app.Welcome Text",
          "value": "欢迎使用智能采购推荐系统！请描述您的采购需求，我将为您推荐最适合的供应商。"
        },
        {
          "key": "variables",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "core.app.Chat Variable",
          "value": []
        },
        {
          "key": "questionGuide",
          "valueType": "any",
          "renderTypeList": [
            "hidden"
          ],
          "label": "core.app.Question Guide",
          "value": {
            "open": true,
            "textList": [
              "我需要采购钢材，要求质量好，交期快",
              "帮我推荐塑料原料供应商",
              "查找电子元器件的优质供应商"
            ]
          }
        },
        {
          "key": "tts",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "",
          "value": {
            "type": "web"
          }
        },
        {
          "key": "whisper",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "",
          "value": {
            "open": false,
            "autoSend": false,
            "autoTTSResponse": false
          }
        },
        {
          "key": "scheduleTrigger",
          "renderTypeList": [
            "hidden"
          ],
          "valueType": "any",
          "label": "",
          "value": null
        }
      ],
      "outputs": []
    },
    {
      "nodeId": "448745",
      "name": "common:core.module.template.work_start",
      "intro": "",
      "avatar": "core/workflow/template/workflowStart",
      "flowNodeType": "workflowStart",
      "position": {
        "x": 300,
        "y": -200
      },
      "version": "481",
      "inputs": [
        {
          "key": "userChatInput",
          "renderTypeList": [
            "reference",
            "textarea"
          ],
          "valueType": "string",
          "label": "common:core.module.input.label.user question",
          "required": true,
          "toolDescription": "用户问题",
          "debugLabel": ""
        }
      ],
      "outputs": [
        {
          "id": "userChatInput",
          "key": "userChatInput",
          "label": "common:core.module.input.label.user question",
          "type": "static",
          "valueType": "string",
          "description": ""
        }
      ]
    },
    {
      "nodeId": "extractInfo",
      "name": "采购需求信息提取",
      "intro": "提取用户采购需求的详细信息",
      "avatar": "core/workflow/template/extractJson",
      "flowNodeType": "contentExtract",
      "showStatus": true,
      "position": {
        "x": 600,
        "y": -200
      },
      "version": "4.9.2",
      "inputs": [
        {
          "key": "model",
          "renderTypeList": [
            "selectLLMModel",
            "reference"
          ],
          "label": "AI 模型",
          "required": true,
          "valueType": "string",
          "llmModelType": "extractFields",
          "value": "qwen-max"
        },
        {
          "key": "description",
          "renderTypeList": [
            "textarea",
            "reference"
          ],
          "valueType": "string",
          "label": "提取要求描述",
          "value": "从用户的采购需求中提取关键信息，包括材料类型、数量、质量要求、交期、预算等。如果某些信息未明确提及，请标记为'未指定'。"
        },
        {
          "key": "history",
          "renderTypeList": [
            "numberInput",
            "reference"
          ],
          "valueType": "chatHistory",
          "label": "聊天记录",
          "required": true,
          "min": 0,
          "max": 50,
          "value": 6
        },
        {
          "key": "content",
          "renderTypeList": [
            "reference",
            "textarea"
          ],
          "label": "需要提取的文本",
          "required": true,
          "valueType": "string",
          "value": [
            "448745",
            "userChatInput"
          ]
        },
        {
          "key": "extractKeys",
          "renderTypeList": [
            "custom"
          ],
          "label": "",
          "valueType": "any",
          "value": [
            {
              "valueType": "string",
              "required": true,
              "defaultValue": "",
              "desc": "采购材料名称",
              "key": "材料名称",
              "enum": ""
            },
            {
              "valueType": "string",
              "required": false,
              "defaultValue": "未指定",
              "desc": "采购数量",
              "key": "采购数量",
              "enum": ""
            },
            {
              "valueType": "string",
              "required": false,
              "defaultValue": "未指定",
              "desc": "质量标准要求",
              "key": "质量要求",
              "enum": ""
            },
            {
              "valueType": "string",
              "required": false,
              "defaultValue": "未指定",
              "desc": "交付时间要求",
              "key": "交期要求",
              "enum": ""
            },
            {
              "valueType": "string",
              "required": false,
              "defaultValue": "未指定",
              "desc": "预算范围",
              "key": "预算范围",
              "enum": ""
            }
          ]
        }
      ],
      "outputs": [
        {
          "id": "success",
          "key": "success",
          "label": "字段完全提取",
          "required": true,
          "valueType": "boolean",
          "type": "static"
        },
        {
          "id": "fields",
          "key": "fields",
          "label": "完整提取结果",
          "required": true,
          "valueType": "string",
          "type": "static"
        },
        {
          "id": "materialName",
          "key": "材料名称",
          "label": "提取结果-材料名称",
          "valueType": "string",
          "type": "static"
        },
        {
          "id": "quantity",
          "key": "采购数量",
          "label": "提取结果-采购数量",
          "valueType": "string",
          "type": "static"
        },
        {
          "id": "quality",
          "key": "质量要求",
          "label": "提取结果-质量要求",
          "valueType": "string",
          "type": "static"
        },
        {
          "id": "delivery",
          "key": "交期要求",
          "label": "提取结果-交期要求",
          "valueType": "string",
          "type": "static"
        },
        {
          "id": "budget",
          "key": "预算范围",
          "label": "提取结果-预算范围",
          "valueType": "string",
          "type": "static"
        }
      ]
    },

    {
      "nodeId": "searchSuppliers",
      "name": "供应商信息搜索",
      "intro": "搜索相关供应商信息",
      "avatar": "core/workflow/template/datasetSearch",
      "flowNodeType": "datasetSearchNode",
      "showStatus": true,
      "position": {
        "x": 1200,
        "y": -200
      },
      "version": "4.9.2",
      "inputs": [
        {
          "key": "datasets",
          "renderTypeList": [
            "selectDataset",
            "reference"
          ],
          "label": "选择知识库",
          "value": [],
          "valueType": "selectDataset",
          "required": true
        },
        {
          "key": "similarity",
          "renderTypeList": [
            "selectDatasetParamsModal"
          ],
          "label": "",
          "value": 0.7,
          "valueType": "number"
        },
        {
          "key": "limit",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": 20,
          "valueType": "number"
        },
        {
          "key": "searchMode",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "string",
          "value": "hybrid"
        },
        {
          "key": "embeddingWeight",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "number",
          "value": 0.6
        },
        {
          "key": "usingReRank",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "boolean",
          "value": true
        },
        {
          "key": "rerankWeight",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "number",
          "value": 0.7
        },
        {
          "key": "datasetSearchUsingExtensionQuery",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "boolean",
          "value": true
        },
        {
          "key": "userChatInput",
          "renderTypeList": [
            "reference",
            "textarea"
          ],
          "valueType": "string",
          "label": "用户问题",
          "required": true,
          "value": [
            "extractInfo",
            "materialName"
          ]
        }
      ],
      "outputs": [
        {
          "id": "quoteQA",
          "key": "quoteQA",
          "label": "知识库引用",
          "type": "static",
          "valueType": "datasetQuote"
        }
      ]
    },
    {
      "nodeId": "searchHistory",
      "name": "历史采购记录搜索",
      "intro": "搜索相关的历史采购记录",
      "avatar": "core/workflow/template/datasetSearch",
      "flowNodeType": "datasetSearchNode",
      "showStatus": true,
      "position": {
        "x": 1200,
        "y": 100
      },
      "version": "4.9.2",
      "inputs": [
        {
          "key": "datasets",
          "renderTypeList": [
            "selectDataset",
            "reference"
          ],
          "label": "选择知识库",
          "value": [],
          "valueType": "selectDataset",
          "required": true
        },
        {
          "key": "similarity",
          "renderTypeList": [
            "selectDatasetParamsModal"
          ],
          "label": "",
          "value": 0.6,
          "valueType": "number"
        },
        {
          "key": "limit",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "value": 15,
          "valueType": "number"
        },
        {
          "key": "searchMode",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "string",
          "value": "hybrid"
        },
        {
          "key": "usingReRank",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "boolean",
          "value": true
        },
        {
          "key": "userChatInput",
          "renderTypeList": [
            "reference",
            "textarea"
          ],
          "valueType": "string",
          "label": "用户问题",
          "required": true,
          "value": [
            "extractInfo",
            "materialName"
          ]
        }
      ],
      "outputs": [
        {
          "id": "quoteQA",
          "key": "quoteQA",
          "label": "知识库引用",
          "type": "static",
          "valueType": "datasetQuote"
        }
      ]
    },
    {
      "nodeId": "analyzeSuppliers",
      "name": "供应商分析评分",
      "intro": "分析供应商信息并进行评分",
      "avatar": "core/workflow/template/aiChat",
      "flowNodeType": "chatNode",
      "showStatus": true,
      "position": {
        "x": 1500,
        "y": -50
      },
      "version": "4.9.7",
      "inputs": [
        {
          "key": "model",
          "renderTypeList": [
            "settingLLMModel",
            "reference"
          ],
          "label": "AI 模型",
          "valueType": "string",
          "value": "qwen-max"
        },
        {
          "key": "temperature",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "number",
          "value": 0.3
        },
        {
          "key": "systemPrompt",
          "renderTypeList": [
            "textarea",
            "reference"
          ],
          "max": 3000,
          "valueType": "string",
          "label": "提示词",
          "value": "你是专业的采购助手。请根据用户需求和搜索到的信息进行分析：\n\n用户原始需求：{{$448745.userChatInput$}}\n\n提取的采购信息：\n- 材料：{{$extractInfo.材料名称$}}\n- 数量：{{$extractInfo.采购数量$}}\n- 质量要求：{{$extractInfo.质量要求$}}\n- 交期要求：{{$extractInfo.交期要求$}}\n- 预算范围：{{$extractInfo.预算范围$}}\n\n供应商信息：\n{{$searchSuppliers.quoteQA$}}\n\n历史采购记录：\n{{$searchHistory.quoteQA$}}\n\n请按以下逻辑处理：\n\n1. 如果材料名称为空或\"未指定\"，说明信息提取失败，请提示用户提供更详细的采购需求信息。\n\n2. 如果找到了相关供应商信息，请按以下格式输出分析结果：\n\n## 供应商评分分析\n\n### 评分维度说明\n- 产品质量 (25%)\n- 价格竞争力 (20%)\n- 交付能力 (20%)\n- 服务质量 (15%)\n- 企业信誉 (10%)\n- 技术实力 (10%)\n\n### 供应商评分表\n| 供应商名称 | 产品质量 | 价格竞争力 | 交付能力 | 服务质量 | 企业信誉 | 技术实力 | 综合评分 | 推荐等级 |\n|-----------|---------|-----------|---------|---------|---------|---------|---------|----------|\n| 供应商A   | 8.5     | 7.0       | 9.0     | 8.0     | 8.5     | 7.5     | 8.1     | 优先推荐 |\n\n### 详细分析\n对每个供应商的优势、劣势进行详细说明。\n\n### 推荐建议\n基于评分结果给出最终推荐意见。\n\n3. 如果没有找到相关供应商信息，请说明原因并建议用户提供更具体的材料信息。"
        },
        {
          "key": "history",
          "renderTypeList": [
            "numberInput",
            "reference"
          ],
          "valueType": "chatHistory",
          "label": "聊天记录",
          "required": true,
          "min": 0,
          "max": 50,
          "value": 6
        },
        {
          "key": "userChatInput",
          "renderTypeList": [
            "reference",
            "textarea"
          ],
          "valueType": "string",
          "label": "用户问题",
          "required": true,
          "value": "请对找到的供应商进行分析评分"
        }
      ],
      "outputs": [
        {
          "id": "history",
          "key": "history",
          "required": true,
          "label": "新的上下文",
          "valueType": "chatHistory",
          "type": "static"
        },
        {
          "id": "answerText",
          "key": "answerText",
          "required": true,
          "label": "AI 回复内容",
          "valueType": "string",
          "type": "static"
        }
      ]
    },
    {
      "nodeId": "generateReport",
      "name": "生成推荐报告",
      "intro": "生成最终的采购推荐报告",
      "avatar": "core/workflow/template/aiChat",
      "flowNodeType": "chatNode",
      "showStatus": true,
      "position": {
        "x": 1800,
        "y": -50
      },
      "version": "4.9.7",
      "inputs": [
        {
          "key": "model",
          "renderTypeList": [
            "settingLLMModel",
            "reference"
          ],
          "label": "AI 模型",
          "valueType": "string",
          "value": "qwen-max"
        },
        {
          "key": "temperature",
          "renderTypeList": [
            "hidden"
          ],
          "label": "",
          "valueType": "number",
          "value": 0.2
        },
        {
          "key": "systemPrompt",
          "renderTypeList": [
            "textarea",
            "reference"
          ],
          "max": 3000,
          "valueType": "string",
          "label": "提示词",
          "value": "基于供应商分析结果，生成专业的采购推荐报告。\n\n## 采购推荐报告\n\n### 采购需求概述\n- **采购材料**：{{$extractInfo.材料名称$}}\n- **采购数量**：{{$extractInfo.采购数量$}}\n- **质量要求**：{{$extractInfo.质量要求$}}\n- **交期要求**：{{$extractInfo.交期要求$}}\n- **预算范围**：{{$extractInfo.预算范围$}}\n\n### 供应商评估结果\n{{$analyzeSuppliers.answerText$}}\n\n### 风险提示\n- 请在签约前进一步核实供应商资质\n- 建议进行样品测试确认产品质量\n- 关注市场价格波动对采购成本的影响\n\n### 后续建议\n1. 与推荐供应商进行详细商务谈判\n2. 建立供应商评估档案\n3. 制定供应商管理和监督机制\n\n---\n*本报告基于现有数据生成，仅供参考。最终采购决策请结合实际情况综合考虑。*"
        },
        {
          "key": "history",
          "renderTypeList": [
            "numberInput",
            "reference"
          ],
          "valueType": "chatHistory",
          "label": "聊天记录",
          "required": true,
          "min": 0,
          "max": 50,
          "value": [
            "analyzeSuppliers",
            "history"
          ]
        },
        {
          "key": "userChatInput",
          "renderTypeList": [
            "reference",
            "textarea"
          ],
          "valueType": "string",
          "label": "用户问题",
          "required": true,
          "value": [
            "448745",
            "userChatInput"
          ]
        }
      ],
      "outputs": [
        {
          "id": "history",
          "key": "history",
          "required": true,
          "label": "新的上下文",
          "valueType": "chatHistory",
          "type": "static"
        },
        {
          "id": "answerText",
          "key": "answerText",
          "required": true,
          "label": "AI 回复内容",
          "valueType": "string",
          "type": "static"
        }
              ]
      }
    ],
  "edges": [
    {
      "source": "448745",
      "target": "extractInfo",
      "sourceHandle": "448745-source-right",
      "targetHandle": "extractInfo-target-left"
    },
    {
      "source": "extractInfo",
      "target": "searchSuppliers",
      "sourceHandle": "extractInfo-source-right",
      "targetHandle": "searchSuppliers-target-left"
    },
    {
      "source": "extractInfo",
      "target": "searchHistory",
      "sourceHandle": "extractInfo-source-right",
      "targetHandle": "searchHistory-target-left"
    },
    {
      "source": "searchSuppliers",
      "target": "analyzeSuppliers",
      "sourceHandle": "searchSuppliers-source-right",
      "targetHandle": "analyzeSuppliers-target-left"
    },
    {
      "source": "searchHistory",
      "target": "analyzeSuppliers",
      "sourceHandle": "searchHistory-source-right",
      "targetHandle": "analyzeSuppliers-target-top"
    },
    {
      "source": "analyzeSuppliers",
      "target": "generateReport",
      "sourceHandle": "analyzeSuppliers-source-right",
      "targetHandle": "generateReport-target-left"
    }
  ],
  "chatConfig": {
    "variables": [],
    "scheduledTriggerConfig": {
      "cronString": "",
      "timezone": "Asia/Shanghai",
      "defaultPrompt": ""
    },
    "_id": "6828c51eb044efef46b4eb1a"
  }
}