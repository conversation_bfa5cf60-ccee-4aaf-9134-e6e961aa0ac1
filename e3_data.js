/**
 * 计算一组数的均值和方差
 * @param numbers 数字数组
 * @returns 包含均值和方差的对象
 */
function calculateMeanAndVariance(input) {
    // 将逗号分隔的字符串转换为数字数组
    var numbers = input.split(',').map(function (num) { return parseFloat(num.trim()); });
    // 检查是否有无效数字
    if (numbers.some(isNaN)) {
        throw new Error("输入包含无效数字");
    }
    if (numbers.length === 0) {
        throw new Error("数组不能为空");
    }
    // 计算均值
    var sum = numbers.reduce(function (acc, val) { return acc + val; }, 0);
    var mean = sum / numbers.length;
    // 计算方差
    var squaredDiffs = numbers.map(function (num) { return Math.pow(num - mean, 2); });
    var variance = squaredDiffs.reduce(function (acc, val) { return acc + val; }, 0) / numbers.length;
    return { mean: mean, variance: variance };
}
