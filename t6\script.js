// 全局变量
let logData = null;
let isScrollPaused = false;
let logScrollInterval = null;
let currentLogIndex = 0;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initDashboard();
    loadLogData();
    startTimeUpdate();
});

// 初始化仪表盘
function initDashboard() {
    console.log('初始化仪表盘...');
    
    // 绑定事件监听器
    document.getElementById('pause-scroll').addEventListener('click', toggleLogScroll);
    document.getElementById('close-modal').addEventListener('click', closeModal);
    
    // 点击模态窗口外部关闭
    document.getElementById('device-modal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeModal();
        }
    });
}

// 加载日志数据
async function loadLogData() {
    try {
        console.log('加载日志分析数据...');
        const response = await fetch('log_analysis_results.json');
        logData = await response.json();
        console.log('数据加载成功:', logData);
        
        // 更新所有组件
        updateStatCards();
        renderCharts();
        renderDeviceRanking();
        startLogScroll();
        
    } catch (error) {
        console.error('加载数据失败:', error);
        showError('无法加载日志数据，请检查文件是否存在');
    }
}

// 更新统计卡片
function updateStatCards() {
    if (!logData) return;
    
    const { summary } = logData;
    
    // 数字动画效果
    animateNumber('total-logs', 0, logData.total_logs, 2000);
    animateNumber('total-devices', 0, summary.total_devices, 2000);
    animateNumber('total-errors', 0, summary.total_errors, 2000);
    animateNumber('total-warnings', 0, summary.total_warnings, 2000);
    animateNumber('total-faults', 0, summary.total_faults, 2000);
    
    // 计算故障率
    const faultRate = ((summary.total_faults / logData.total_logs) * 100).toFixed(2);
    animateNumber('fault-rate', 0, parseFloat(faultRate), 2000, '%');
    
    // 更新数据更新时间
    document.getElementById('update-time').textContent = logData.analysis_time;
}

// 数字动画效果
function animateNumber(elementId, start, end, duration, suffix = '') {
    const element = document.getElementById(elementId);
    const range = end - start;
    const increment = range / (duration / 16);
    let current = start;
    
    const timer = setInterval(() => {
        current += increment;
        if (current >= end) {
            current = end;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current).toLocaleString() + suffix;
    }, 16);
}

// 渲染所有图表
function renderCharts() {
    if (!logData) return;
    
    renderFaultTypesChart();
    renderHourlyTrendChart();
    renderErrorTypesChart();
}

// 渲染故障类型分布饼图
function renderFaultTypesChart() {
    const chartDom = document.getElementById('fault-types-chart');
    const myChart = echarts.init(chartDom);
    
    const data = Object.entries(logData.fault_types).map(([name, value]) => ({
        name: name,
        value: value
    }));
    
    const option = {
        backgroundColor: 'transparent',
        tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#409eff',
            textStyle: { color: '#ffffff' }
        },
        legend: {
            orient: 'vertical',
            left: 'left',
            textStyle: { color: '#a0cfff' }
        },
        series: [{
            name: '故障类型',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['60%', '50%'],
            avoidLabelOverlap: false,
            itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
            },
            label: {
                show: false,
                position: 'center'
            },
            emphasis: {
                label: {
                    show: true,
                    fontSize: '16',
                    fontWeight: 'bold',
                    color: '#ffffff'
                }
            },
            labelLine: {
                show: false
            },
            data: data,
            color: ['#ff6b6b', '#ffa726', '#ffcc02', '#66bb6a', '#42a5f5', '#ab47bc']
        }]
    };
    
    myChart.setOption(option);
    
    // 响应式
    window.addEventListener('resize', () => myChart.resize());
}

// 渲染时间趋势图
function renderHourlyTrendChart() {
    const chartDom = document.getElementById('hourly-trend-chart');
    const myChart = echarts.init(chartDom);
    
    const hours = [];
    const errorData = [];
    const warnData = [];
    
    for (let i = 0; i < 24; i++) {
        const hour = i.toString().padStart(2, '0');
        hours.push(hour + ':00');
        const hourData = logData.hourly_stats[hour] || { error: 0, warn: 0 };
        errorData.push(hourData.error || 0);
        warnData.push(hourData.warn || 0);
    }
    
    const option = {
        backgroundColor: 'transparent',
        tooltip: {
            trigger: 'axis',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#409eff',
            textStyle: { color: '#ffffff' }
        },
        legend: {
            data: ['错误', '警告'],
            textStyle: { color: '#a0cfff' }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: hours,
            axisLine: { lineStyle: { color: '#409eff' } },
            axisLabel: { color: '#a0cfff' }
        },
        yAxis: {
            type: 'value',
            axisLine: { lineStyle: { color: '#409eff' } },
            axisLabel: { color: '#a0cfff' },
            splitLine: { lineStyle: { color: 'rgba(64, 158, 255, 0.2)' } }
        },
        series: [{
            name: '错误',
            type: 'line',
            stack: 'Total',
            smooth: true,
            lineStyle: { color: '#f56c6c', width: 3 },
            areaStyle: { color: 'rgba(245, 108, 108, 0.3)' },
            data: errorData
        }, {
            name: '警告',
            type: 'line',
            stack: 'Total',
            smooth: true,
            lineStyle: { color: '#e6a23c', width: 3 },
            areaStyle: { color: 'rgba(230, 162, 60, 0.3)' },
            data: warnData
        }]
    };
    
    myChart.setOption(option);
    
    // 响应式
    window.addEventListener('resize', () => myChart.resize());
}

// 渲染错误类型分布图
function renderErrorTypesChart() {
    const chartDom = document.getElementById('error-types-chart');
    const myChart = echarts.init(chartDom);
    
    const data = Object.entries(logData.error_types).map(([name, value]) => ({
        name: name,
        value: value
    }));
    
    const option = {
        backgroundColor: 'transparent',
        tooltip: {
            trigger: 'item',
            backgroundColor: 'rgba(0, 0, 0, 0.8)',
            borderColor: '#409eff',
            textStyle: { color: '#ffffff' }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            axisLine: { lineStyle: { color: '#409eff' } },
            axisLabel: { color: '#a0cfff' },
            splitLine: { lineStyle: { color: 'rgba(64, 158, 255, 0.2)' } }
        },
        yAxis: {
            type: 'category',
            data: data.map(item => item.name),
            axisLine: { lineStyle: { color: '#409eff' } },
            axisLabel: { color: '#a0cfff' }
        },
        series: [{
            name: '错误数量',
            type: 'bar',
            data: data.map(item => item.value),
            itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                    { offset: 0, color: '#f56c6c' },
                    { offset: 1, color: '#ff9a9e' }
                ])
            },
            barWidth: '60%'
        }]
    };
    
    myChart.setOption(option);
    
    // 响应式
    window.addEventListener('resize', () => myChart.resize());
}

// 渲染设备排行榜
function renderDeviceRanking() {
    if (!logData || !logData.device_faults) return;
    
    const deviceFaultCounts = Object.entries(logData.device_faults)
        .map(([deviceId, faults]) => ({
            deviceId: deviceId,
            faultCount: faults.length
        }))
        .sort((a, b) => b.faultCount - a.faultCount)
        .slice(0, 10);
    
    const container = document.getElementById('device-ranking');
    container.innerHTML = '';
    
    deviceFaultCounts.forEach((item, index) => {
        const rankingItem = document.createElement('div');
        rankingItem.className = 'ranking-item';
        rankingItem.innerHTML = `
            <div class="ranking-rank">${index + 1}</div>
            <div class="ranking-device">设备 ${item.deviceId}</div>
            <div class="ranking-count">${item.faultCount}次</div>
        `;
        
        // 点击事件 - 显示设备详情
        rankingItem.addEventListener('click', () => showDeviceDetails(item.deviceId));
        
        container.appendChild(rankingItem);
    });
}

// 显示设备详情
function showDeviceDetails(deviceId) {
    if (!logData || !logData.device_faults[deviceId]) return;
    
    const faults = logData.device_faults[deviceId];
    const modal = document.getElementById('device-modal');
    const title = document.getElementById('modal-device-title');
    const content = document.getElementById('modal-device-content');
    
    title.textContent = `设备 ${deviceId} 详细信息`;
    
    // 统计故障类型
    const faultTypes = {};
    faults.forEach(fault => {
        faultTypes[fault.fault] = (faultTypes[fault.fault] || 0) + 1;
    });
    
    content.innerHTML = `
        <div style="margin-bottom: 20px;">
            <h4 style="color: #ffffff; margin-bottom: 10px;">故障统计</h4>
            <p>总故障次数: <span style="color: #f56c6c; font-weight: bold;">${faults.length}</span></p>
            <p>涉及端口数: <span style="color: #67c23a; font-weight: bold;">${new Set(faults.map(f => f.port)).size}</span></p>
        </div>
        
        <div style="margin-bottom: 20px;">
            <h4 style="color: #ffffff; margin-bottom: 10px;">故障类型分布</h4>
            ${Object.entries(faultTypes).map(([type, count]) => `
                <div style="margin-bottom: 5px;">
                    <span style="color: #a0cfff;">${type}:</span> 
                    <span style="color: #e6a23c; font-weight: bold;">${count}次</span>
                </div>
            `).join('')}
        </div>
        
        <div>
            <h4 style="color: #ffffff; margin-bottom: 10px;">最近故障记录</h4>
            <div style="max-height: 200px; overflow-y: auto;">
                ${faults.slice(0, 10).map(fault => `
                    <div style="padding: 8px; margin-bottom: 5px; background: rgba(64, 158, 255, 0.1); border-radius: 5px; border-left: 3px solid #e6a23c;">
                        <div style="color: #a0cfff; font-size: 0.9rem;">${fault.time}</div>
                        <div style="color: #ffffff;">端口${fault.port}: ${fault.fault}</div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
    
    modal.style.display = 'block';
}

// 关闭模态窗口
function closeModal() {
    document.getElementById('device-modal').style.display = 'none';
}

// 开始日志滚动
function startLogScroll() {
    if (!logData || !logData.device_faults) return;
    
    // 将所有故障信息转换为日志格式
    const allLogs = [];
    Object.entries(logData.device_faults).forEach(([deviceId, faults]) => {
        faults.forEach(fault => {
            allLogs.push({
                time: fault.time,
                device: deviceId,
                port: fault.port,
                message: fault.fault,
                type: 'warn'
            });
        });
    });
    
    // 按时间排序
    allLogs.sort((a, b) => new Date(a.time) - new Date(b.time));
    
    currentLogIndex = 0;
    
    // 开始滚动显示日志
    logScrollInterval = setInterval(() => {
        if (!isScrollPaused && allLogs.length > 0) {
            const log = allLogs[currentLogIndex % allLogs.length];
            addLogItem(log);
            currentLogIndex++;
        }
    }, 1000);
}

// 添加日志项
function addLogItem(log) {
    const container = document.getElementById('logs-content');
    const logItem = document.createElement('div');
    logItem.className = `log-item ${log.type}`;
    
    logItem.innerHTML = `
        <span class="log-time">${log.time}</span>
        <span class="log-device">设备${log.device}</span>
        <span class="log-message">端口${log.port} ${log.message}</span>
    `;
    
    container.appendChild(logItem);
    
    // 保持最多显示30条日志
    const logItems = container.children;
    if (logItems.length > 30) {
        container.removeChild(logItems[0]);
    }
    
    // 自动滚动到底部
    container.scrollTop = container.scrollHeight;
}

// 切换日志滚动状态
function toggleLogScroll() {
    isScrollPaused = !isScrollPaused;
    const button = document.getElementById('pause-scroll');
    button.textContent = isScrollPaused ? '继续滚动' : '暂停滚动';
    button.style.background = isScrollPaused ? 
        'linear-gradient(45deg, #f56c6c, #e6a23c)' : 
        'linear-gradient(45deg, #409eff, #67c23a)';
}

// 开始时间更新
function startTimeUpdate() {
    function updateTime() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        document.getElementById('current-time').textContent = timeString;
    }
    
    updateTime();
    setInterval(updateTime, 1000);
}

// 显示错误信息
function showError(message) {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
        position: fixed;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        background: rgba(245, 108, 108, 0.9);
        color: white;
        padding: 20px;
        border-radius: 10px;
        font-size: 1.2rem;
        z-index: 10000;
        text-align: center;
        min-width: 300px;
    `;
    errorDiv.textContent = message;
    
    document.body.appendChild(errorDiv);
    
    setTimeout(() => {
        document.body.removeChild(errorDiv);
    }, 5000);
}

// 工具函数 - 数字格式化
function formatNumber(num) {
    return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
}

// 工具函数 - 获取随机颜色
function getRandomColor() {
    const colors = ['#ff6b6b', '#ffa726', '#ffcc02', '#66bb6a', '#42a5f5', '#ab47bc'];
    return colors[Math.floor(Math.random() * colors.length)];
}

// 页面可见性改变时的处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        // 页面隐藏时暂停动画
        isScrollPaused = true;
    } else {
        // 页面显示时恢复动画
        isScrollPaused = false;
    }
});

// 键盘快捷键
document.addEventListener('keydown', function(e) {
    switch(e.key) {
        case 'Escape':
            closeModal();
            break;
        case ' ':
            e.preventDefault();
            toggleLogScroll();
            break;
        case 'r':
        case 'R':
            if (e.ctrlKey) {
                e.preventDefault();
                location.reload();
            }
            break;
    }
});

// 页面卸载时清理
window.addEventListener('beforeunload', function() {
    if (logScrollInterval) {
        clearInterval(logScrollInterval);
    }
}); 