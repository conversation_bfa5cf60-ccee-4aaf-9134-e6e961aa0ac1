def calculate_mean_and_variance(input_str):
    # 将逗号分隔的字符串转换为数字数组
    try:
        numbers = [float(num.strip()) for num in input_str.split(',')]
    except ValueError:
        raise ValueError("输入包含无效数字")

    # 检查是否为空数组
    if not numbers:
        raise ValueError("数组不能为空")

    # 计算均值
    n = len(numbers)
    mean = sum(numbers) / n

    # 计算方差
    squared_diff_sum = sum((x - mean) ** 2 for x in numbers)
    variance = squared_diff_sum / n

    return {"mean": mean, "variance": variance}

# 使用示例
if __name__ == "__main__":
    try:
        input_str = input("请输入一组用逗号分隔的数字: ")
        result = calculate_mean_and_variance(input_str)
        print(f"均值: {result['mean']}")
        print(f"方差: {result['variance']}")
    except ValueError as e:
        print(f"错误: {str(e)}")
