/**
 * 身份证有效性判断
 * @param idCard 身份证号码
 * @returns 是否有效
 */
function isValidIDCard(idCard: string): boolean {
    // 基本格式校验
    if (!/^\d{17}[\dXx]$/.test(idCard)) {
      return false;
    }
  
    // 提取省份代码
    const provinceCode = idCard.substring(0, 2);
    const provinceCodes = [
      "11", "12", "13", "14", "15", "21", "22", "23",
      "31", "32", "33", "34", "35", "36", "37", "41",
      "42", "43", "44", "45", "46", "50", "51", "52",
      "53", "54", "61", "62", "63", "64", "65", "71",
      "81", "82"
    ];
    if (!provinceCodes.includes(provinceCode)) {
      return false;
    }
  
    // 出生日期校验
    const year = parseInt(idCard.substring(6, 10));
    const month = parseInt(idCard.substring(10, 12));
    const day = parseInt(idCard.substring(12, 14));
  
    if (month < 1 || month > 12) {
      return false;
    }
  
    const maxDays = new Date(year, month, 0).getDate();
    if (day < 1 || day > maxDays) {
      return false;
    }
  
    // 校验码校验
    if (idCard.length === 18) {
      const factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      const checkCodes = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
      
      let sum = 0;
      for (let i = 0; i < 17; i++) {
        sum += parseInt(idCard.charAt(i)) * factors[i];
      }
      
      const checkCode = checkCodes[sum % 11];
      return checkCode === idCard.charAt(17).toUpperCase();
    }
  
    return true;
  }
  
  // 使用示例
  // 从命令行读取输入
  const readline = require('readline').createInterface({
    input: process.stdin,
    output: process.stdout
  });

  readline.question('请输入身份证号: ', (idCard) => {
    if(isValidIDCard(idCard)){
        console.log(`${idCard} 是有效的身份证号`);
    }else{
        console.log(`${idCard} 是无效的身份证号`);
    }
    readline.close();
  });
// 110101199003074258输出：true 有效身份证
// 12345678901234567X输出: false 无效身份证